using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace SportProductApp.ViewComponents
{
    public class CategorySelectorViewComponent : ViewComponent
    {
        private readonly List<string> _categories = new List<string>
        {
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>", 
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Quần áo"
        };

        public IViewComponentResult Invoke(string selectedCategory = "", string controlName = "Category", bool useRadioButtons = false)
        {
            var model = new CategorySelectorViewModel
            {
                Categories = _categories.Select(c => new SelectListItem
                {
                    Text = c,
                    Value = c,
                    Selected = !string.IsNullOrWhiteSpace(selectedCategory) && c.Equals(selectedCategory, StringComparison.OrdinalIgnoreCase)
                }).ToList(),
                SelectedCategory = selectedCategory,
                ControlName = controlName,
                UseRadioButtons = useRadioButtons
            };

            return View(model);
        }
    }

    public class CategorySelectorViewModel
    {
        public List<SelectListItem> Categories { get; set; } = new List<SelectListItem>();
        public string SelectedCategory { get; set; } = string.Empty;
        public string ControlName { get; set; } = "Category";
        public bool UseRadioButtons { get; set; } = false;
    }
}
