using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SportProductApp.Models
{
    public class Product
    {
        [Key]
        public int ProductID { get; set; }

        [Display(Name = "Tên sản phẩm")]
        public string? NamePro { get; set; }

        [Display(Name = "Mô tả")]
        [Column("DecriptionPro")] // Match the database column name with typo
        public string? DecriptionPro { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn danh mục")]
        [Display(Name = "Danh mục")]
        [CategoryValidation(ErrorMessage = "Danh mục phải là một trong các giá trị: Vợt, Bóng, Cầ<PERSON>, Đệm, Quần áo")]
        public string? Category { get; set; }

        [Display(Name = "Giá")]
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal? Price { get; set; }

        [Display(Name = "Hình ảnh")]
        public string? ImagePro { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn ngày sản xuất")]
        [Display(Name = "Ngày sản xuất")]
        [Column(TypeName = "date")]
        [PastDateValidation(ErrorMessage = "Ngày sản xuất phải nhỏ hơn ngày hiện tại")]
        public DateTime ManufacturingDate { get; set; }
    }

    // Custom validation attribute for Category
    public class CategoryValidationAttribute : ValidationAttribute
    {
        private readonly string[] _validCategories = { "Vợt", "Bóng", "Cầu", "Đệm", "Quần áo" };

        public override bool IsValid(object? value)
        {
            if (value == null) return false;
            return _validCategories.Contains(value.ToString()?.Trim());
        }
    }

    // Custom validation attribute for Past Date
    public class PastDateValidationAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is DateTime date)
            {
                return date < DateTime.Today;
            }
            return false;
        }
    }
}
