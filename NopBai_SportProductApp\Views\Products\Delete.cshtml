@model SportProductApp.Models.Product

@{
    ViewData["Title"] = "Xóa sản phẩm";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="delete-product-card">
                <div class="card shadow-lg border-danger">
                    <div class="card-header bg-danger text-white">
                        <h2 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Xác nhận xóa sản phẩm
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-warning me-2"></i>
                            <strong>Cảnh báo!</strong> Bạn có chắc chắn muốn xóa sản phẩm này? Hành động này không thể hoàn tác.
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="product-image-container">
                                    @if (!string.IsNullOrEmpty(Model.ImagePro))
                                    {
                                        <img src="@Model.ImagePro" alt="@Model.NamePro" class="product-image img-fluid rounded shadow">
                                    }
                                    else
                                    {
                                        <div class="no-image-placeholder">
                                            <i class="fas fa-image fa-4x text-muted"></i>
                                            <p class="text-muted mt-2">Không có hình ảnh</p>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="product-info">
                                    <div class="info-item">
                                        <label class="info-label">Mã sản phẩm:</label>
                                        <span class="info-value badge bg-secondary">#@Model.ProductID</span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="info-label">Tên sản phẩm:</label>
                                        <span class="info-value product-name">@Model.NamePro</span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="info-label">Danh mục:</label>
                                        <span class="info-value category-badge badge bg-info">@Model.Category</span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="info-label">Giá:</label>
                                        <span class="info-value price">@Model.Price?.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="info-label">Ngày sản xuất:</label>
                                        <span class="info-value">@Model.ManufacturingDate.ToString("dd/MM/yyyy")</span>
                                    </div>
                                    
                                    @if (!string.IsNullOrEmpty(Model.DecriptionPro))
                                    {
                                        <div class="info-item description-item">
                                            <label class="info-label">Mô tả:</label>
                                            <div class="info-value description-text">@Model.DecriptionPro</div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                        
                        <div class="confirmation-section mt-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label" for="confirmDelete">
                                    Tôi hiểu rằng hành động này sẽ xóa vĩnh viễn sản phẩm "<strong>@Model.NamePro</strong>" và không thể hoàn tác.
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <a asp-action="Index" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Quay lại danh sách
                                </a>
                                <a asp-action="Details" asp-route-id="@Model.ProductID" class="btn btn-outline-info ms-2">
                                    <i class="fas fa-eye me-1"></i>
                                    Xem chi tiết
                                </a>
                            </div>
                            <div>
                                <form asp-action="Delete" method="post" id="deleteForm" class="d-inline">
                                    <input type="hidden" asp-for="ProductID" />
                                    <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                        <i class="fas fa-trash me-1"></i>
                                        Xóa sản phẩm
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .delete-product-card {
        margin-bottom: 2rem;
    }
    
    .product-image-container {
        padding: 1rem;
    }
    
    .product-image {
        max-width: 100%;
        max-height: 250px;
        object-fit: cover;
        border: 2px solid #dee2e6;
    }
    
    .no-image-placeholder {
        padding: 2rem;
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
        text-align: center;
    }
    
    .product-info {
        padding: 1rem 0;
    }
    
    .info-item {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        display: block;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-value {
        font-size: 1.1rem;
        color: #212529;
    }
    
    .product-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #dc3545;
    }
    
    .price {
        font-size: 1.2rem;
        font-weight: 700;
        color: #dc3545;
    }
    
    .category-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    
    .description-item {
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 2px solid #e9ecef;
    }
    
    .description-text {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        border-left: 4px solid #dc3545;
        line-height: 1.6;
    }
    
    .confirmation-section {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.375rem;
        padding: 1rem;
        border-left: 4px solid #ffc107;
    }
    
    .form-check-input:checked {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    .card {
        border-radius: 1rem;
    }
    
    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }
    
    .card-footer {
        border-radius: 0 0 1rem 1rem !important;
        padding: 1.5rem;
    }
    
    .alert-warning {
        border-left: 4px solid #ffc107;
    }
    
    #deleteButton:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    @@media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }
        
        .product-name {
            font-size: 1.1rem;
        }
        
        .price {
            font-size: 1rem;
        }
    }
</style>

@section Scripts {
    <script>
        // Enable/disable delete button based on confirmation checkbox
        document.getElementById('confirmDelete').addEventListener('change', function() {
            const deleteButton = document.getElementById('deleteButton');
            deleteButton.disabled = !this.checked;
            
            if (this.checked) {
                deleteButton.classList.remove('btn-outline-danger');
                deleteButton.classList.add('btn-danger');
            } else {
                deleteButton.classList.remove('btn-danger');
                deleteButton.classList.add('btn-outline-danger');
            }
        });

        // Confirmation dialog before deletion
        document.getElementById('deleteForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const productName = '@Model.NamePro';
            const confirmMessage = `Bạn có THỰC SỰ chắc chắn muốn xóa sản phẩm "${productName}"?\n\nHành động này KHÔNG THỂ hoàn tác!`;
            
            if (confirm(confirmMessage)) {
                // Show loading state
                const deleteButton = document.getElementById('deleteButton');
                const originalText = deleteButton.innerHTML;
                deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang xóa...';
                deleteButton.disabled = true;
                
                // Submit the form
                this.submit();
            }
        });

        // Auto-focus on confirmation checkbox
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.getElementById('confirmDelete').focus();
            }, 500);
        });

        // Keyboard shortcut for quick actions
        document.addEventListener('keydown', function(e) {
            // Escape key to go back
            if (e.key === 'Escape') {
                window.location.href = '@Url.Action("Index")';
            }
            
            // Enter key to toggle confirmation (when checkbox is focused)
            if (e.key === 'Enter' && document.activeElement.id === 'confirmDelete') {
                e.preventDefault();
                document.getElementById('confirmDelete').click();
            }
        });
    </script>
}
