@model SportProductApp.Models.Product

@{
    ViewData["Title"] = "Chỉnh sửa sản phẩm";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="edit-product-card">
                <div class="card shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h2 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Chỉnh sửa sản phẩm
                        </h2>
                    </div>
                    <div class="card-body">
                        <form asp-action="Edit" method="post" id="editForm" class="needs-validation" novalidate>
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                            
                            <input type="hidden" asp-for="ProductID" />
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="NamePro" class="form-label required"></label>
                                        <input asp-for="NamePro" class="form-control" placeholder="Nhập tên sản phẩm" />
                                        <span asp-validation-for="NamePro" class="text-danger"></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="Price" class="form-label required"></label>
                                        <div class="input-group">
                                            <span class="input-group-text">₫</span>
                                            <input asp-for="Price" class="form-control" placeholder="0" type="number" step="0.01" min="0" />
                                        </div>
                                        <span asp-validation-for="Price" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="Category" class="form-label required"></label>
                                        @await Component.InvokeAsync("CategorySelector", new { selectedCategory = Model.Category ?? "", controlName = "Category", useRadioButtons = false })
                                        <span asp-validation-for="Category" class="text-danger"></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="ManufacturingDate" class="form-label required"></label>
                                        <input asp-for="ManufacturingDate" class="form-control" type="date" max="@DateTime.Today.ToString("yyyy-MM-dd")" />
                                        <span asp-validation-for="ManufacturingDate" class="text-danger"></span>
                                        <div class="form-text">Ngày sản xuất phải nhỏ hơn ngày hiện tại</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label asp-for="ImagePro" class="form-label"></label>
                                <input asp-for="ImagePro" class="form-control" placeholder="Nhập URL hình ảnh" />
                                <span asp-validation-for="ImagePro" class="text-danger"></span>
                                <div class="form-text">Nhập đường dẫn URL của hình ảnh sản phẩm</div>
                            </div>
                            
                            <div class="form-group mb-4">
                                <label asp-for="DecriptionPro" class="form-label"></label>
                                <textarea asp-for="DecriptionPro" class="form-control" rows="4" placeholder="Nhập mô tả chi tiết về sản phẩm..."></textarea>
                                <span asp-validation-for="DecriptionPro" class="text-danger"></span>
                            </div>
                            
                            <!-- Image Preview -->
                            <div class="form-group mb-3" id="imagePreviewContainer" style="display: none;">
                                <label class="form-label">Xem trước hình ảnh:</label>
                                <div class="image-preview-wrapper">
                                    <img id="imagePreview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Hủy bỏ
                                    </a>
                                    <div>
                                        <a asp-action="Details" asp-route-id="@Model.ProductID" class="btn btn-outline-info me-2">
                                            <i class="fas fa-eye me-1"></i>
                                            Xem chi tiết
                                        </a>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-1"></i>
                                            Lưu thay đổi
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .edit-product-card {
        margin-bottom: 2rem;
    }

    .card {
        border: none;
        border-radius: 1rem;
    }

    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }

    .form-label.required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-control:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }

    .form-select:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }

    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
        font-weight: 600;
    }

    .form-actions {
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 2px solid #e9ecef;
    }

    .image-preview-wrapper {
        text-align: center;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        border: 2px dashed #dee2e6;
    }

    .alert-danger {
        border-left: 4px solid #dc3545;
    }

    .text-danger {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .was-validated .form-control:valid {
        border-color: #198754;
    }

    .was-validated .form-control:invalid {
        border-color: #dc3545;
    }

    .was-validated .form-select:valid {
        border-color: #198754;
    }

    .was-validated .form-select:invalid {
        border-color: #dc3545;
    }

    @@media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }

        .card-body {
            padding: 1rem;
        }
    }
</style>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/js/product-validation.js" asp-append-version="true"></script>

    <script>
        // Image preview functionality
        document.getElementById('ImagePro').addEventListener('input', function() {
            const url = this.value;
            const preview = document.getElementById('imagePreview');
            const container = document.getElementById('imagePreviewContainer');
            
            if (url && isValidImageUrl(url)) {
                preview.src = url;
                container.style.display = 'block';
                
                preview.onerror = function() {
                    container.style.display = 'none';
                };
            } else {
                container.style.display = 'none';
            }
        });
        
        function isValidImageUrl(url) {
            return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url) || url.includes('http');
        }
        
        // Initialize image preview on page load
        document.addEventListener('DOMContentLoaded', function() {
            const imageInput = document.getElementById('ImagePro');
            if (imageInput.value) {
                imageInput.dispatchEvent(new Event('input'));
            }
        });
        
        // Custom validation for manufacturing date
        document.getElementById('ManufacturingDate').addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate >= today) {
                this.setCustomValidity('Ngày sản xuất phải nhỏ hơn ngày hiện tại');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Bootstrap form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
}
