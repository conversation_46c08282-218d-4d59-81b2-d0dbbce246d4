@model IEnumerable<SportProductApp.Models.Product>

@{
    ViewData["Title"] = "Danh sách sản phẩm";
}

@Html.AntiForgeryToken()

<div class="container mt-4">
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6">
            <i class="fas fa-box-open me-2 text-primary"></i>
            Danh sách sản phẩm thể thao
            @if (Model != null && Model.Any())
            {
                <span class="badge bg-primary ms-2">@Model.Count()</span>
            }
        </h1>
        <div>
            <a asp-action="FirstProduct" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i>
                Sản phẩm đầu tiên
            </a>
            <a asp-action="Create" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>
                Thêm sản phẩm
            </a>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="row">
            @foreach (var item in Model)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100 shadow-sm">
                        <div class="card-img-container">
                            @if (!string.IsNullOrEmpty(item.ImagePro))
                            {
                                <img src="@item.ImagePro" class="card-img-top" alt="@item.NamePro">
                            }
                            else
                            {
                                <div class="no-image-placeholder">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-primary">#@item.ProductID</span>
                                <span class="badge bg-info">@item.Category</span>
                            </div>
                            <h5 class="card-title">@item.NamePro</h5>
                            <p class="card-text text-muted small">
                                @if (!string.IsNullOrEmpty(item.DecriptionPro))
                                {
                                    @(item.DecriptionPro.Length > 100 ? item.DecriptionPro.Substring(0, 100) + "..." : item.DecriptionPro)
                                }
                                else
                                {
                                    <em>Không có mô tả</em>
                                }
                            </p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="price-tag">@item.Price?.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                    <small class="text-muted">@item.ManufacturingDate.ToString("dd/MM/yyyy")</small>
                                </div>
                                <div class="btn-group w-100" role="group">
                                    <a asp-action="Details" asp-route-id="@item.ProductID"
                                       class="btn btn-outline-primary btn-sm"
                                       title="Xem chi tiết sản phẩm"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.ProductID"
                                       class="btn btn-outline-warning btn-sm"
                                       title="Chỉnh sửa sản phẩm"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-outline-danger btn-sm quick-delete-btn"
                                            data-product-id="@item.ProductID"
                                            data-product-name="@item.NamePro"
                                            title="Xóa sản phẩm"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <div class="empty-state">
                <i class="fas fa-box-open fa-5x text-muted mb-3"></i>
                <h3 class="text-muted">Chưa có sản phẩm nào</h3>
                <p class="text-muted">Hãy thêm sản phẩm đầu tiên của bạn!</p>
                <a asp-action="Create" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    Thêm sản phẩm đầu tiên
                </a>
            </div>
        </div>
    }
</div>

<style>
    .product-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        border-radius: 1rem;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .card-img-container {
        height: 200px;
        overflow: hidden;
        border-radius: 1rem 1rem 0 0;
        position: relative;
    }
    
    .card-img-top {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .product-card:hover .card-img-top {
        transform: scale(1.05);
    }
    
    .no-image-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .price-tag {
        font-size: 1.2rem;
        font-weight: 700;
        color: #dc3545;
    }
    
    .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.5rem;
    }
    
    .empty-state {
        padding: 3rem 1rem;
    }
    
    .btn-group .btn {
        flex: 1;
    }
    
    .badge {
        font-size: 0.75rem;
        margin-right: 0.25rem;
    }
    
    @@media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
        
        .display-6 {
            font-size: 1.5rem;
        }
    }
</style>

@section Scripts {
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // Quick delete functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.quick-delete-btn')) {
                e.preventDefault();
                const button = e.target.closest('.quick-delete-btn');
                const productId = button.getAttribute('data-product-id');
                const productName = button.getAttribute('data-product-name');

                if (confirm(`Bạn có chắc chắn muốn xóa sản phẩm "${productName}"?\n\nHành động này không thể hoàn tác!`)) {
                    // Show loading state
                    const originalContent = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    button.disabled = true;

                    // Create form and submit
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '@Url.Action("QuickDelete")';

                    const tokenInput = document.createElement('input');
                    tokenInput.type = 'hidden';
                    tokenInput.name = '__RequestVerificationToken';
                    tokenInput.value = document.querySelector('input[name="__RequestVerificationToken"]').value;

                    const idInput = document.createElement('input');
                    idInput.type = 'hidden';
                    idInput.name = 'id';
                    idInput.value = productId;

                    form.appendChild(tokenInput);
                    form.appendChild(idInput);
                    document.body.appendChild(form);

                    // Submit via AJAX
                    fetch(form.action, {
                        method: 'POST',
                        body: new FormData(form),
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove the product card with animation
                            const productCard = button.closest('.col-lg-4');
                            productCard.style.transition = 'all 0.3s ease';
                            productCard.style.transform = 'scale(0.8)';
                            productCard.style.opacity = '0';

                            setTimeout(() => {
                                productCard.remove();
                                showToast(data.message, 'success');

                                // Update product count
                                const badge = document.querySelector('.badge.bg-primary');
                                if (badge) {
                                    const currentCount = parseInt(badge.textContent);
                                    if (currentCount > 1) {
                                        badge.textContent = currentCount - 1;
                                    } else {
                                        // Reload page if no products left
                                        location.reload();
                                    }
                                }
                            }, 300);
                        } else {
                            showToast(data.message, 'error');
                            button.innerHTML = originalContent;
                            button.disabled = false;
                        }
                    })
                    .catch(error => {
                        showToast('Có lỗi xảy ra khi xóa sản phẩm.', 'error');
                        button.innerHTML = originalContent;
                        button.disabled = false;
                    })
                    .finally(() => {
                        document.body.removeChild(form);
                    });
                }
            }
        });

        // Toast notification function
        function showToast(message, type = 'info') {
            const toastContainer = document.querySelector('.toast-container') || createToastContainer();

            const toastId = 'toast-' + Date.now();
            const iconClass = type === 'success' ? 'fa-check-circle text-success' :
                             type === 'error' ? 'fa-exclamation-circle text-danger' :
                             'fa-info-circle text-info';

            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.id = toastId;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="toast-header">
                    <i class="fas ${iconClass} me-2"></i>
                    <strong class="me-auto">Thông báo</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">${message}</div>
            `;

            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast element after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
            return container;
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>
}
