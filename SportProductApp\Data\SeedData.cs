using Microsoft.EntityFrameworkCore;
using SportProductApp.Models;

namespace SportProductApp.Data
{
    public static class SeedData
    {
        public static void Initialize(IServiceProvider serviceProvider)
        {
            using (var context = new ApplicationDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>()))
            {
                // Check if any products exist
                if (context.Products.Any())
                {
                    return; // DB has been seeded
                }

                context.Products.AddRange(
                    new Product
                    {
                        NamePro = "Vợt Tennis Wilson Pro Staff",
                        DecriptionPro = "Vợt tennis chuyên nghiệp Wilson Pro Staff với công nghệ tiên tiến, phù hợp cho người chơi trung cấp và cao cấp.",
                        Category = "Vợt",
                        Price = 2500000,
                        ImagePro = "https://images.unsplash.com/photo-**********-1dfe5d97d256?w=400",
                        ManufacturingDate = new DateTime(2023, 6, 15)
                    },
                    new Product
                    {
                        NamePro = "Bóng đá FIFA World Cup",
                        DecriptionPro = "Bóng đá chính thức FIFA World Cup với chất liệu da cao cấp, đảm bảo độ bền và cảm giác tốt khi chơi.",
                        Category = "Bóng",
                        Price = 850000,
                        ImagePro = "https://images.unsplash.com/photo-1614632537190-23e4b2e69c88?w=400",
                        ManufacturingDate = new DateTime(2023, 8, 20)
                    },
                    new Product
                    {
                        NamePro = "Cầu lông Yonex Aerosensa",
                        DecriptionPro = "Cầu lông Yonex Aerosensa chất lượng cao với lông ngỗng tự nhiên, bay ổn định và bền bỉ.",
                        Category = "Cầu",
                        Price = 180000,
                        ImagePro = "https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?w=400",
                        ManufacturingDate = new DateTime(2023, 9, 10)
                    },
                    new Product
                    {
                        NamePro = "Đệm tập Yoga Premium",
                        DecriptionPro = "Đệm tập yoga cao cấp với chất liệu TPE thân thiện môi trường, chống trượt và êm ái.",
                        Category = "Đệm",
                        Price = 450000,
                        ImagePro = "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400",
                        ManufacturingDate = new DateTime(2023, 7, 5)
                    },
                    new Product
                    {
                        NamePro = "Áo thể thao Nike Dri-FIT",
                        DecriptionPro = "Áo thể thao Nike Dri-FIT với công nghệ thấm hút mồ hôi, thoáng khí và thoải mái khi vận động.",
                        Category = "Quần áo",
                        Price = 680000,
                        ImagePro = "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400",
                        ManufacturingDate = new DateTime(2023, 5, 25)
                    }
                );

                context.SaveChanges();
            }
        }
    }
}
