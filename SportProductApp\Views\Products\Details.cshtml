@model SportProductApp.Models.Product

@{
    ViewData["Title"] = "Chi tiết sản phẩm";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            @if (Model == null)
            {
                <div class="alert alert-warning text-center">
                    <h4>@ViewBag.Message</h4>
                    <a asp-action="Index" class="btn btn-primary">Quay lại danh sách</a>
                </div>
            }
            else
            {
                <div class="product-detail-card">
                    <div class="card shadow-lg">
                        <div class="card-header bg-primary text-white">
                            <h2 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Chi tiết sản phẩm
                            </h2>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <div class="product-image-container">
                                        @if (!string.IsNullOrEmpty(Model.ImagePro))
                                        {
                                            <img src="@Model.ImagePro" alt="@Model.NamePro" class="product-image img-fluid rounded shadow">
                                        }
                                        else
                                        {
                                            <div class="no-image-placeholder">
                                                <i class="fas fa-image fa-5x text-muted"></i>
                                                <p class="text-muted mt-2">Không có hình ảnh</p>
                                            </div>
                                        }
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="product-info">
                                        <div class="info-item">
                                            <label class="info-label">Mã sản phẩm:</label>
                                            <span class="info-value badge bg-secondary">#@Model.ProductID</span>
                                        </div>
                                        
                                        <div class="info-item">
                                            <label class="info-label">Tên sản phẩm:</label>
                                            <span class="info-value product-name">@Model.NamePro</span>
                                        </div>
                                        
                                        <div class="info-item">
                                            <label class="info-label">Danh mục:</label>
                                            <span class="info-value category-badge badge bg-info">@Model.Category</span>
                                        </div>
                                        
                                        <div class="info-item">
                                            <label class="info-label">Giá:</label>
                                            <span class="info-value price">@Model.Price?.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                        </div>
                                        
                                        <div class="info-item">
                                            <label class="info-label">Ngày sản xuất:</label>
                                            <span class="info-value">@Model.ManufacturingDate.ToString("dd/MM/yyyy")</span>
                                        </div>
                                        
                                        @if (!string.IsNullOrEmpty(Model.DecriptionPro))
                                        {
                                            <div class="info-item description-item">
                                                <label class="info-label">Mô tả:</label>
                                                <div class="info-value description-text">@Model.DecriptionPro</div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-light">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Quay lại danh sách
                                </a>
                                <div>
                                    <a asp-action="Edit" asp-route-id="@Model.ProductID" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>
                                        Chỉnh sửa
                                    </a>
                                    <a asp-action="FirstProduct" class="btn btn-primary">
                                        <i class="fas fa-eye me-1"></i>
                                        Sản phẩm đầu tiên
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
    .product-detail-card {
        margin-bottom: 2rem;
    }
    
    .product-image-container {
        padding: 1rem;
    }
    
    .product-image {
        max-width: 100%;
        max-height: 300px;
        object-fit: cover;
        border: 2px solid #dee2e6;
    }
    
    .no-image-placeholder {
        padding: 2rem;
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
        text-align: center;
    }
    
    .product-info {
        padding: 1rem 0;
    }
    
    .info-item {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        display: block;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-value {
        font-size: 1.1rem;
        color: #212529;
    }
    
    .product-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #0d6efd;
    }
    
    .price {
        font-size: 1.3rem;
        font-weight: 700;
        color: #dc3545;
    }
    
    .category-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    
    .description-item {
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 2px solid #e9ecef;
    }
    
    .description-text {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        border-left: 4px solid #0d6efd;
        line-height: 1.6;
    }
    
    .card {
        border: none;
        border-radius: 1rem;
    }
    
    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }
    
    .card-footer {
        border-radius: 0 0 1rem 1rem !important;
        padding: 1.5rem;
    }
    
    @media (max-width: 768px) {
        .product-name {
            font-size: 1.2rem;
        }
        
        .price {
            font-size: 1.1rem;
        }
        
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
