@model SportProductApp.ViewComponents.CategorySelectorViewModel

@if (Model.UseRadioButtons)
{
    <div class="category-radio-group">
        @foreach (var category in Model.Categories)
        {
            <div class="form-check">
                <input class="form-check-input" 
                       type="radio" 
                       name="@Model.ControlName" 
                       id="@(Model.ControlName)<EMAIL>" 
                       value="@category.Value" 
                       @(category.Selected ? "checked" : "")>
                <label class="form-check-label" for="@(Model.ControlName)<EMAIL>">
                    @category.Text
                </label>
            </div>
        }
    </div>
}
else
{
    <select class="form-select" asp-for="@Model.ControlName" name="@Model.ControlName" id="@Model.ControlName">
        <option value="">-- Ch<PERSON><PERSON> danh mục --</option>
        @foreach (var category in Model.Categories)
        {
            @if (category.Selected)
            {
                <option value="@category.Value" selected>@category.Text</option>
            }
            else
            {
                <option value="@category.Value">@category.Text</option>
            }
        }
    </select>
}

<style>
    .category-radio-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .form-check {
        margin-right: 15px;
    }
</style>
