// Custom validation for SportProduct application

$(document).ready(function() {
    // Add custom validation methods
    $.validator.addMethod("pastdate", function(value, element) {
        if (!value) return false;
        var selectedDate = new Date(value);
        var today = new Date();
        today.setHours(0, 0, 0, 0);
        return selectedDate < today;
    }, "Ngày sản xuất phải nhỏ hơn ngày hiện tại");

    $.validator.addMethod("validcategory", function(value, element) {
        var validCategories = ["Vợt", "Bóng", "Cầu", "Đệm", "Quần áo"];
        return validCategories.includes(value);
    }, "Vui lòng chọn một danh mục hợp lệ");

    $.validator.addMethod("positivePrice", function(value, element) {
        return value > 0;
    }, "Giá phải lớn hơn 0");

    // Configure validation for the edit form
    $("#editForm").validate({
        rules: {
            NamePro: {
                required: true,
                minlength: 2,
                maxlength: 100
            },
            Category: {
                required: true,
                validcategory: true
            },
            Price: {
                required: true,
                number: true,
                positivePrice: true
            },
            ManufacturingDate: {
                required: true,
                pastdate: true
            },
            ImagePro: {
                url: true
            }
        },
        messages: {
            NamePro: {
                required: "Vui lòng nhập tên sản phẩm",
                minlength: "Tên sản phẩm phải có ít nhất 2 ký tự",
                maxlength: "Tên sản phẩm không được vượt quá 100 ký tự"
            },
            Category: {
                required: "Vui lòng chọn danh mục"
            },
            Price: {
                required: "Vui lòng nhập giá sản phẩm",
                number: "Vui lòng nhập số hợp lệ"
            },
            ManufacturingDate: {
                required: "Vui lòng chọn ngày sản xuất"
            },
            ImagePro: {
                url: "Vui lòng nhập URL hợp lệ"
            }
        },
        errorElement: "span",
        errorClass: "text-danger",
        errorPlacement: function(error, element) {
            error.addClass("invalid-feedback");
            element.closest(".form-group").append(error);
        },
        highlight: function(element, errorClass, validClass) {
            $(element).addClass("is-invalid").removeClass("is-valid");
        },
        unhighlight: function(element, errorClass, validClass) {
            $(element).addClass("is-valid").removeClass("is-invalid");
        },
        submitHandler: function(form) {
            // Add loading state to submit button
            var submitBtn = $(form).find('button[type="submit"]');
            var originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Đang lưu...').prop('disabled', true);
            
            // Submit the form
            form.submit();
        }
    });

    // Real-time validation feedback
    $('input, select, textarea').on('blur', function() {
        $(this).valid();
    });

    // Category change validation
    $('#Category').on('change', function() {
        $(this).valid();
    });

    // Manufacturing date validation with visual feedback
    $('#ManufacturingDate').on('change', function() {
        var selectedDate = new Date(this.value);
        var today = new Date();
        today.setHours(0, 0, 0, 0);
        
        var feedbackElement = $(this).siblings('.date-feedback');
        if (feedbackElement.length === 0) {
            feedbackElement = $('<div class="date-feedback mt-1"></div>');
            $(this).after(feedbackElement);
        }
        
        if (selectedDate >= today) {
            feedbackElement.html('<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Ngày sản xuất phải nhỏ hơn ngày hiện tại</small>');
            $(this).addClass('is-invalid');
        } else {
            var daysDiff = Math.floor((today - selectedDate) / (1000 * 60 * 60 * 24));
            feedbackElement.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>Sản phẩm được sản xuất ' + daysDiff + ' ngày trước</small>');
            $(this).removeClass('is-invalid').addClass('is-valid');
        }
    });

    // Price formatting and validation
    $('#Price').on('input', function() {
        var value = parseFloat(this.value);
        if (!isNaN(value) && value > 0) {
            var formatted = new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(value);
            
            var feedbackElement = $(this).closest('.input-group').siblings('.price-feedback');
            if (feedbackElement.length === 0) {
                feedbackElement = $('<div class="price-feedback mt-1"></div>');
                $(this).closest('.input-group').after(feedbackElement);
            }
            
            feedbackElement.html('<small class="text-info"><i class="fas fa-info-circle me-1"></i>Giá hiển thị: ' + formatted + '</small>');
        }
    });

    // Image URL validation with preview
    $('#ImagePro').on('input', function() {
        var url = this.value.trim();
        var previewContainer = $('#imagePreviewContainer');
        var preview = $('#imagePreview');
        
        if (url && isValidImageUrl(url)) {
            preview.attr('src', url);
            previewContainer.show();
            
            preview.on('load', function() {
                $(this).removeClass('loading-error');
                var feedbackElement = previewContainer.find('.image-feedback');
                if (feedbackElement.length === 0) {
                    feedbackElement = $('<div class="image-feedback mt-2"></div>');
                    previewContainer.append(feedbackElement);
                }
                feedbackElement.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>Hình ảnh tải thành công</small>');
            });
            
            preview.on('error', function() {
                $(this).addClass('loading-error');
                var feedbackElement = previewContainer.find('.image-feedback');
                if (feedbackElement.length === 0) {
                    feedbackElement = $('<div class="image-feedback mt-2"></div>');
                    previewContainer.append(feedbackElement);
                }
                feedbackElement.html('<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Không thể tải hình ảnh từ URL này</small>');
            });
        } else {
            previewContainer.hide();
        }
    });

    function isValidImageUrl(url) {
        return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url) || url.includes('http');
    }

    // Form submission confirmation
    $('#editForm').on('submit', function(e) {
        if ($(this).valid()) {
            var confirmMessage = 'Bạn có chắc chắn muốn lưu những thay đổi này?';
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        }
    });

    // Auto-save draft functionality (optional enhancement)
    var autoSaveTimer;
    $('input, select, textarea').on('input change', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            saveDraft();
        }, 2000);
    });

    function saveDraft() {
        var formData = {
            ProductID: $('#ProductID').val(),
            NamePro: $('#NamePro').val(),
            Category: $('#Category').val(),
            Price: $('#Price').val(),
            ManufacturingDate: $('#ManufacturingDate').val(),
            ImagePro: $('#ImagePro').val(),
            DecriptionPro: $('#DecriptionPro').val()
        };
        
        localStorage.setItem('productDraft_' + formData.ProductID, JSON.stringify(formData));
        
        // Show auto-save indicator
        var indicator = $('.auto-save-indicator');
        if (indicator.length === 0) {
            indicator = $('<div class="auto-save-indicator position-fixed" style="top: 20px; right: 20px; z-index: 1050;"></div>');
            $('body').append(indicator);
        }
        
        indicator.html('<div class="alert alert-info alert-dismissible fade show" role="alert">' +
            '<i class="fas fa-save me-1"></i>Đã tự động lưu bản nháp' +
            '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
            '</div>');
        
        setTimeout(function() {
            indicator.find('.alert').alert('close');
        }, 3000);
    }
});

// Additional utility functions
function showToast(message, type = 'info') {
    var toastContainer = $('.toast-container');
    if (toastContainer.length === 0) {
        toastContainer = $('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
        $('body').append(toastContainer);
    }
    
    var toast = $('<div class="toast" role="alert">' +
        '<div class="toast-header">' +
        '<i class="fas fa-info-circle me-2 text-' + type + '"></i>' +
        '<strong class="me-auto">Thông báo</strong>' +
        '<button type="button" class="btn-close" data-bs-dismiss="toast"></button>' +
        '</div>' +
        '<div class="toast-body">' + message + '</div>' +
        '</div>');
    
    toastContainer.append(toast);
    var bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();
}
