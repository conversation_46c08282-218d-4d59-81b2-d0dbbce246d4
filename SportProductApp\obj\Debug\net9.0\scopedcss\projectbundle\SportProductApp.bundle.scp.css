/* _content/SportProductApp/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-noqs6ktbpz] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-noqs6ktbpz] {
  color: #0077cc;
}

.btn-primary[b-noqs6ktbpz] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-noqs6ktbpz], .nav-pills .show > .nav-link[b-noqs6ktbpz] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-noqs6ktbpz] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-noqs6ktbpz] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-noqs6ktbpz] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-noqs6ktbpz] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-noqs6ktbpz] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
