@model SportProductApp.Models.Product

@{
    ViewData["Title"] = "Thêm sản phẩm mới";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="create-product-card">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white">
                        <h2 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            Thêm sản phẩm mới
                        </h2>
                    </div>
                    <div class="card-body">
                        <form asp-action="Create" method="post" id="createForm" class="needs-validation" novalidate>
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="NamePro" class="form-label required"></label>
                                        <input asp-for="NamePro" class="form-control" placeholder="Nhập tên sản phẩm" required />
                                        <span asp-validation-for="NamePro" class="text-danger"></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="Price" class="form-label required"></label>
                                        <div class="input-group">
                                            <span class="input-group-text">₫</span>
                                            <input asp-for="Price" class="form-control" placeholder="0" type="number" step="0.01" min="0" required />
                                        </div>
                                        <span asp-validation-for="Price" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="Category" class="form-label required"></label>
                                        @await Component.InvokeAsync("CategorySelector", new { selectedCategory = "", controlName = "Category", useRadioButtons = false })
                                        <span asp-validation-for="Category" class="text-danger"></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label asp-for="ManufacturingDate" class="form-label required"></label>
                                        <input asp-for="ManufacturingDate" class="form-control" type="date" max="@DateTime.Today.ToString("yyyy-MM-dd")" required />
                                        <span asp-validation-for="ManufacturingDate" class="text-danger"></span>
                                        <div class="form-text">Ngày sản xuất phải nhỏ hơn ngày hiện tại</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label asp-for="ImagePro" class="form-label"></label>
                                <input asp-for="ImagePro" class="form-control" placeholder="Nhập URL hình ảnh" />
                                <span asp-validation-for="ImagePro" class="text-danger"></span>
                                <div class="form-text">Nhập đường dẫn URL của hình ảnh sản phẩm</div>
                            </div>
                            
                            <div class="form-group mb-4">
                                <label asp-for="DecriptionPro" class="form-label"></label>
                                <textarea asp-for="DecriptionPro" class="form-control" rows="4" placeholder="Nhập mô tả chi tiết về sản phẩm..."></textarea>
                                <span asp-validation-for="DecriptionPro" class="text-danger"></span>
                            </div>
                            
                            <!-- Image Preview -->
                            <div class="form-group mb-3" id="imagePreviewContainer" style="display: none;">
                                <label class="form-label">Xem trước hình ảnh:</label>
                                <div class="image-preview-wrapper">
                                    <img id="imagePreview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
                                </div>
                            </div>
                            
                            <!-- Sample Data Button -->
                            <div class="form-group mb-3">
                                <button type="button" class="btn btn-outline-info btn-sm" id="fillSampleData">
                                    <i class="fas fa-magic me-1"></i>
                                    Điền dữ liệu mẫu
                                </button>
                            </div>
                            
                            <div class="form-actions">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Hủy bỏ
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-warning me-2">
                                            <i class="fas fa-undo me-1"></i>
                                            Đặt lại
                                        </button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-plus me-1"></i>
                                            Thêm sản phẩm
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .create-product-card {
        margin-bottom: 2rem;
    }
    
    .card {
        border: none;
        border-radius: 1rem;
    }
    
    .card-header {
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }
    
    .form-label.required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }
    
    .form-control:focus {
        border-color: #198754;
        box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
    }
    
    .form-select:focus {
        border-color: #198754;
        box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
        font-weight: 600;
    }
    
    .form-actions {
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 2px solid #e9ecef;
    }
    
    .image-preview-wrapper {
        text-align: center;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        border: 2px dashed #dee2e6;
    }
    
    .alert-danger {
        border-left: 4px solid #dc3545;
    }
    
    .text-danger {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .form-text {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .was-validated .form-control:valid {
        border-color: #198754;
    }
    
    .was-validated .form-control:invalid {
        border-color: #dc3545;
    }
    
    .was-validated .form-select:valid {
        border-color: #198754;
    }
    
    .was-validated .form-select:invalid {
        border-color: #dc3545;
    }
    
    @@media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }
        
        .card-body {
            padding: 1rem;
        }
    }
</style>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/js/product-validation.js" asp-append-version="true"></script>
    
    <script>
        // Sample data for testing
        const sampleProducts = [
            {
                name: "Vợt Cầu Lông Yonex Arcsaber",
                description: "Vợt cầu lông chuyên nghiệp Yonex Arcsaber với công nghệ tiên tiến, phù hợp cho người chơi trung cấp và cao cấp.",
                category: "Vợt",
                price: 1200000,
                image: "https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?w=400",
                date: "2023-08-15"
            },
            {
                name: "Bóng Rổ Spalding NBA",
                description: "Bóng rổ chính thức NBA Spalding với chất liệu da cao cấp, đảm bảo độ bền và cảm giác tốt khi chơi.",
                category: "Bóng",
                price: 950000,
                image: "https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400",
                date: "2023-09-20"
            },
            {
                name: "Đệm Tập Thể Dục Đa Năng",
                description: "Đệm tập thể dục đa năng với chất liệu NBR cao cấp, chống trượt và êm ái cho mọi bài tập.",
                category: "Đệm",
                price: 350000,
                image: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400",
                date: "2023-07-10"
            }
        ];

        document.getElementById('fillSampleData').addEventListener('click', function() {
            const randomProduct = sampleProducts[Math.floor(Math.random() * sampleProducts.length)];
            
            document.getElementById('NamePro').value = randomProduct.name;
            document.getElementById('DecriptionPro').value = randomProduct.description;
            document.getElementById('Category').value = randomProduct.category;
            document.getElementById('Price').value = randomProduct.price;
            document.getElementById('ImagePro').value = randomProduct.image;
            document.getElementById('ManufacturingDate').value = randomProduct.date;
            
            // Trigger image preview
            document.getElementById('ImagePro').dispatchEvent(new Event('input'));
            
            // Show success message
            showToast('Đã điền dữ liệu mẫu thành công!', 'success');
        });

        // Form reset confirmation
        document.querySelector('button[type="reset"]').addEventListener('click', function(e) {
            if (!confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu đã nhập?')) {
                e.preventDefault();
            } else {
                document.getElementById('imagePreviewContainer').style.display = 'none';
            }
        });

        // Form submission confirmation
        document.getElementById('createForm').addEventListener('submit', function(e) {
            if (this.checkValidity()) {
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang thêm...';
                submitBtn.disabled = true;
                
                // Re-enable button after 5 seconds in case of error
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            }
        });
    </script>
}
