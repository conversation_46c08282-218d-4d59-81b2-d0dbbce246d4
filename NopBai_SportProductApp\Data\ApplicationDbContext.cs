using Microsoft.EntityFrameworkCore;
using SportProductApp.Models;

namespace SportProductApp.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Product entity to match existing database schema
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.ProductID);

                entity.Property(e => e.NamePro)
                    .HasColumnType("nvarchar(max)");

                entity.Property(e => e.DecriptionPro)
                    .HasColumnName("DecriptionPro")
                    .HasColumnType("nvarchar(max)");

                entity.Property(e => e.Category)
                    .HasColumnType("nchar(20)");

                entity.Property(e => e.Price)
                    .HasColumnType("decimal(18,2)");

                entity.Property(e => e.ImagePro)
                    .HasColumnType("nvarchar(max)");

                entity.Property(e => e.ManufacturingDate)
                    .HasColumnType("date")
                    .IsRequired();
            });
        }
    }
}
