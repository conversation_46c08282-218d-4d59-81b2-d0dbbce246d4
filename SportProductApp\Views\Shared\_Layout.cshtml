﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - SportProductApp</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/SportProductApp.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-primary shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-dumbbell me-2"></i>
                    SportProduct
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-white" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>
                                Trang chủ
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-box-open me-1"></i>
                                Sản phẩm
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Index">
                                    <i class="fas fa-list me-1"></i>
                                    Danh sách sản phẩm
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="FirstProduct">
                                    <i class="fas fa-eye me-1"></i>
                                    Sản phẩm đầu tiên
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Create">
                                    <i class="fas fa-plus me-1"></i>
                                    Thêm sản phẩm
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container-fluid">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-dumbbell me-2"></i>SportProduct</h5>
                    <p class="mb-0">Hệ thống quản lý sản phẩm thể thao</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2025 - SportProductApp</p>
                    <small class="text-muted">Phát triển bởi ASP.NET Core MVC</small>
                </div>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
