@model IEnumerable<SportProductApp.Models.Product>

@{
    ViewData["Title"] = "Danh sách sản phẩm";
}

@Html.AntiForgeryToken()

<div class="container mt-4">
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6">
            <i class="fas fa-box-open me-2 text-primary"></i>
            Danh sách sản phẩm thể thao
            @if (Model != null && Model.Any())
            {
                <span class="badge bg-primary ms-2">@Model.Count()</span>
            }
        </h1>
        <div>
            <a asp-action="FirstProduct" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i>
                Sản phẩm đầu tiên
            </a>
            <a asp-action="Create" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>
                Thêm sản phẩm
            </a>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="row g-4">
            @foreach (var item in Model)
            {
                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                    <div class="card product-card h-100 shadow-sm border-0">
                        <div class="position-relative overflow-hidden">
                            <div class="card-img-container">
                                @if (!string.IsNullOrEmpty(item.ImagePro))
                                {
                                    <img src="@item.ImagePro" class="card-img-top product-image" alt="@item.NamePro" loading="lazy">
                                    <div class="image-overlay">
                                        <div class="overlay-content">
                                            <a asp-action="Details" asp-route-id="@item.ProductID" class="btn btn-light btn-sm rounded-pill">
                                                <i class="fas fa-eye me-1"></i>Xem chi tiết
                                            </a>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="no-image-placeholder">
                                        <div class="placeholder-content">
                                            <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                            <p class="text-muted mb-0">Không có hình ảnh</p>
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="product-badges">
                                <span class="badge bg-primary position-absolute top-0 start-0 m-2">#@item.ProductID</span>
                                <span class="badge bg-success position-absolute top-0 end-0 m-2">@item.Category</span>
                            </div>
                        </div>

                        <div class="card-body d-flex flex-column p-3">
                            <h5 class="card-title product-name mb-2 text-truncate" title="@item.NamePro">@item.NamePro</h5>

                            <p class="card-text description-text text-muted small mb-3">
                                @if (!string.IsNullOrEmpty(item.DecriptionPro))
                                {
                                    @(item.DecriptionPro.Length > 80 ? item.DecriptionPro.Substring(0, 80) + "..." : item.DecriptionPro)
                                }
                                else
                                {
                                    <em class="text-muted">Chưa có mô tả</em>
                                }
                            </p>

                            <div class="product-info mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="price-section">
                                        <span class="price-tag fw-bold">@item.Price?.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                    </div>
                                    <div class="date-section">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            @item.ManufacturingDate.ToString("dd/MM/yyyy")
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-auto">
                                <div class="btn-group w-100" role="group">
                                    <a asp-action="Details" asp-route-id="@item.ProductID"
                                       class="btn btn-outline-primary btn-sm flex-fill"
                                       title="Xem chi tiết sản phẩm"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-eye me-1"></i>
                                        <span class="d-none d-lg-inline">Chi tiết</span>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.ProductID"
                                       class="btn btn-outline-warning btn-sm flex-fill"
                                       title="Chỉnh sửa sản phẩm"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-edit me-1"></i>
                                        <span class="d-none d-lg-inline">Sửa</span>
                                    </a>
                                    <button type="button"
                                            class="btn btn-outline-danger btn-sm flex-fill quick-delete-btn"
                                            data-product-id="@item.ProductID"
                                            data-product-name="@item.NamePro"
                                            title="Xóa sản phẩm"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-trash me-1"></i>
                                        <span class="d-none d-lg-inline">Xóa</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <div class="empty-state">
                <i class="fas fa-box-open fa-5x text-muted mb-3"></i>
                <h3 class="text-muted">Chưa có sản phẩm nào</h3>
                <p class="text-muted">Hãy thêm sản phẩm đầu tiên của bạn!</p>
                <a asp-action="Create" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    Thêm sản phẩm đầu tiên
                </a>
            </div>
        </div>
    }
</div>

<style>
    /* Product Card Styling */
    .product-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 1.25rem;
        overflow: hidden;
        background: #fff;
        border: 1px solid rgba(0,0,0,0.08);
    }

    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
        border-color: rgba(0,0,0,0.12);
    }

    /* Image Container */
    .card-img-container {
        height: 280px;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }

    .product-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
        transition: all 0.4s ease;
        padding: 15px;
        background: #fff;
    }

    .product-card:hover .product-image {
        transform: scale(1.08);
    }

    /* Image Overlay */
    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-card:hover .image-overlay {
        opacity: 1;
    }

    .overlay-content .btn {
        backdrop-filter: blur(10px);
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    /* No Image Placeholder */
    .no-image-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .placeholder-content {
        text-align: center;
        opacity: 0.8;
    }

    /* Product Badges */
    .product-badges .badge {
        font-size: 0.7rem;
        font-weight: 600;
        padding: 0.4rem 0.8rem;
        border-radius: 50px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    /* Card Body */
    .card-body {
        padding: 1.5rem;
    }

    .product-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2d3748;
        line-height: 1.3;
        margin-bottom: 0.75rem;
    }

    .description-text {
        line-height: 1.5;
        color: #718096;
        font-size: 0.9rem;
        min-height: 2.7rem;
    }

    /* Product Info */
    .product-info {
        background: #f7fafc;
        border-radius: 0.75rem;
        padding: 1rem;
        margin: 0 -0.5rem;
    }

    .price-tag {
        font-size: 1.4rem;
        font-weight: 800;
        color: #e53e3e;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .date-section {
        font-size: 0.85rem;
        color: #718096;
    }

    /* Action Buttons */
    .btn-group .btn {
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 0.85rem;
        padding: 0.6rem 0.5rem;
        transition: all 0.2s ease;
        border-width: 1.5px;
    }

    .btn-group .btn:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .btn-group .btn:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .btn-group .btn:not(:first-child):not(:last-child) {
        border-radius: 0;
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        transform: translateY(-1px);
    }

    .btn-outline-warning:hover {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-color: #f093fb;
        transform: translateY(-1px);
    }

    .btn-outline-danger:hover {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        border-color: #ff6b6b;
        transform: translateY(-1px);
    }

    /* Empty State */
    .empty-state {
        padding: 4rem 2rem;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1.5rem;
        color: white;
        margin: 2rem 0;
    }

    .empty-state i {
        opacity: 0.7;
        margin-bottom: 1.5rem;
    }

    .empty-state h3 {
        color: white;
        margin-bottom: 1rem;
    }

    .empty-state p {
        color: rgba(255,255,255,0.8);
        margin-bottom: 2rem;
    }

    /* Responsive Design */
    @@media (max-width: 1200px) {
        .card-img-container {
            height: 250px;
        }

        .product-name {
            font-size: 1.1rem;
        }

        .price-tag {
            font-size: 1.2rem;
        }
    }

    @@media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .display-6 {
            font-size: 1.5rem;
        }

        .card-img-container {
            height: 220px;
        }

        .card-body {
            padding: 1.25rem;
        }

        .btn-group .btn {
            font-size: 0.8rem;
            padding: 0.5rem 0.25rem;
        }

        .btn-group .btn span {
            display: none !important;
        }
    }

    @@media (max-width: 576px) {
        .card-img-container {
            height: 200px;
        }

        .product-info {
            margin: 0;
            padding: 0.75rem;
        }

        .price-tag {
            font-size: 1.1rem;
        }
    }

    /* Loading Animation */
    .product-image[loading="lazy"] {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @@keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Hover Effects */
    .product-card:hover .product-name {
        color: #667eea;
    }

    .product-card:hover .price-tag {
        color: #c53030;
        transform: scale(1.05);
    }

    /* Badge Animations */
    .product-badges .badge {
        animation: fadeInDown 0.6s ease-out;
    }

    .product-badges .badge:nth-child(2) {
        animation-delay: 0.1s;
    }

    @@keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

@section Scripts {
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // Quick delete functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.quick-delete-btn')) {
                e.preventDefault();
                const button = e.target.closest('.quick-delete-btn');
                const productId = button.getAttribute('data-product-id');
                const productName = button.getAttribute('data-product-name');

                if (confirm(`Bạn có chắc chắn muốn xóa sản phẩm "${productName}"?\n\nHành động này không thể hoàn tác!`)) {
                    // Show loading state
                    const originalContent = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    button.disabled = true;

                    // Create form and submit
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '@Url.Action("QuickDelete")';

                    const tokenInput = document.createElement('input');
                    tokenInput.type = 'hidden';
                    tokenInput.name = '__RequestVerificationToken';
                    tokenInput.value = document.querySelector('input[name="__RequestVerificationToken"]').value;

                    const idInput = document.createElement('input');
                    idInput.type = 'hidden';
                    idInput.name = 'id';
                    idInput.value = productId;

                    form.appendChild(tokenInput);
                    form.appendChild(idInput);
                    document.body.appendChild(form);

                    // Submit via AJAX
                    fetch(form.action, {
                        method: 'POST',
                        body: new FormData(form),
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove the product card with animation
                            const productCard = button.closest('.col-lg-4');
                            productCard.style.transition = 'all 0.3s ease';
                            productCard.style.transform = 'scale(0.8)';
                            productCard.style.opacity = '0';

                            setTimeout(() => {
                                productCard.remove();
                                showToast(data.message, 'success');

                                // Update product count
                                const badge = document.querySelector('.badge.bg-primary');
                                if (badge) {
                                    const currentCount = parseInt(badge.textContent);
                                    if (currentCount > 1) {
                                        badge.textContent = currentCount - 1;
                                    } else {
                                        // Reload page if no products left
                                        location.reload();
                                    }
                                }
                            }, 300);
                        } else {
                            showToast(data.message, 'error');
                            button.innerHTML = originalContent;
                            button.disabled = false;
                        }
                    })
                    .catch(error => {
                        showToast('Có lỗi xảy ra khi xóa sản phẩm.', 'error');
                        button.innerHTML = originalContent;
                        button.disabled = false;
                    })
                    .finally(() => {
                        document.body.removeChild(form);
                    });
                }
            }
        });

        // Toast notification function
        function showToast(message, type = 'info') {
            const toastContainer = document.querySelector('.toast-container') || createToastContainer();

            const toastId = 'toast-' + Date.now();
            const iconClass = type === 'success' ? 'fa-check-circle text-success' :
                             type === 'error' ? 'fa-exclamation-circle text-danger' :
                             'fa-info-circle text-info';

            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.id = toastId;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="toast-header">
                    <i class="fas ${iconClass} me-2"></i>
                    <strong class="me-auto">Thông báo</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">${message}</div>
            `;

            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast element after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
            return container;
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>
}
