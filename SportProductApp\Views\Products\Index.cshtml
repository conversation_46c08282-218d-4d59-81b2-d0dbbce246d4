@model IEnumerable<SportProductApp.Models.Product>

@{
    ViewData["Title"] = "Danh sách sản phẩm";
}

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6">
            <i class="fas fa-box-open me-2 text-primary"></i>
            Danh sách sản phẩm thể thao
        </h1>
        <div>
            <a asp-action="FirstProduct" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i>
                Sản phẩm đầu tiên
            </a>
            <a asp-action="Create" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>
                Thêm sản phẩm
            </a>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="row">
            @foreach (var item in Model)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100 shadow-sm">
                        <div class="card-img-container">
                            @if (!string.IsNullOrEmpty(item.ImagePro))
                            {
                                <img src="@item.ImagePro" class="card-img-top" alt="@item.NamePro">
                            }
                            else
                            {
                                <div class="no-image-placeholder">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-primary">#@item.ProductID</span>
                                <span class="badge bg-info">@item.Category</span>
                            </div>
                            <h5 class="card-title">@item.NamePro</h5>
                            <p class="card-text text-muted small">
                                @if (!string.IsNullOrEmpty(item.DecriptionPro))
                                {
                                    @(item.DecriptionPro.Length > 100 ? item.DecriptionPro.Substring(0, 100) + "..." : item.DecriptionPro)
                                }
                                else
                                {
                                    <em>Không có mô tả</em>
                                }
                            </p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="price-tag">@item.Price?.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                    <small class="text-muted">@item.ManufacturingDate.ToString("dd/MM/yyyy")</small>
                                </div>
                                <div class="btn-group w-100" role="group">
                                    <a asp-action="Details" asp-route-id="@item.ProductID" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.ProductID" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.ProductID" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <div class="empty-state">
                <i class="fas fa-box-open fa-5x text-muted mb-3"></i>
                <h3 class="text-muted">Chưa có sản phẩm nào</h3>
                <p class="text-muted">Hãy thêm sản phẩm đầu tiên của bạn!</p>
                <a asp-action="Create" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    Thêm sản phẩm đầu tiên
                </a>
            </div>
        </div>
    }
</div>

<style>
    .product-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        border-radius: 1rem;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .card-img-container {
        height: 200px;
        overflow: hidden;
        border-radius: 1rem 1rem 0 0;
        position: relative;
    }
    
    .card-img-top {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .product-card:hover .card-img-top {
        transform: scale(1.05);
    }
    
    .no-image-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .price-tag {
        font-size: 1.2rem;
        font-weight: 700;
        color: #dc3545;
    }
    
    .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.5rem;
    }
    
    .empty-state {
        padding: 3rem 1rem;
    }
    
    .btn-group .btn {
        flex: 1;
    }
    
    .badge {
        font-size: 0.75rem;
        margin-right: 0.25rem;
    }
    
    @media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
        
        .display-6 {
            font-size: 1.5rem;
        }
    }
</style>
