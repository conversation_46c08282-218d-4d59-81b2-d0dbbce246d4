using Microsoft.EntityFrameworkCore;
using SportProductApp.Models;

namespace SportProductApp.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Product entity
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.ProductID);
                
                entity.Property(e => e.NamePro)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.DescriptionPro)
                    .HasMaxLength(500);

                entity.Property(e => e.Category)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Price)
                    .IsRequired()
                    .HasColumnType("decimal(18,2)");

                entity.Property(e => e.ImagePro)
                    .HasMaxLength(255);

                entity.Property(e => e.ManufacturingDate)
                    .IsRequired();
            });
        }
    }
}
